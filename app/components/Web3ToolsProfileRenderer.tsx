'use client';

import { useState, useEffect } from 'react';
import { Loader2 } from 'lucide-react';
import RenderHero from '@/app/components/renders/render_hero';
import RenderSocialLinks from '@/app/components/renders/render_sociallinks';
import RenderBannerPfp from '@/app/components/renders/render_bannerpfp';

interface ProfileData {
  address: string;
  name?: string;
  profileName?: string;
  bio?: string;
  profileBio?: string;
  chain: string;
  components: {
    componentType: string;
    order: string;
    hidden: string;
    backgroundColor?: string;
    profileName?: string;
    profileBio?: string;
    details?: any;
  }[];
}

export default function Web3ToolsProfileRenderer() {
  const [profileData, setProfileData] = useState<ProfileData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [featuredProfile, setFeaturedProfile] = useState<string>('web3tools');

  useEffect(() => {
    async function loadFeaturedProfile() {
      try {
        // Get the featured profile name from system settings via API
        // Use default chain '25' (Cronos) for featured profile lookup
        const response = await fetch('/api/system-settings/featured-profile?chain=25');
        if (response.ok) {
          const data = await response.json();
          setFeaturedProfile(data.featuredProfile || 'web3tools');
        } else {

        }
      } catch (error) {

        // Keep the default value if there's an error
      }
    }

    loadFeaturedProfile();
  }, []);

  useEffect(() => {
    if (!featuredProfile) return;

    async function fetchProfileData() {
      try {
        setLoading(true);
        setError(null);

        // Fetch profile data for the featured profile
        const response = await fetch(`/api/profile/${featuredProfile}`);

        if (!response.ok) {
          const errorData = await response.json();
          setError(errorData.error || `Failed to load ${featuredProfile} profile`);
          return;
        }

        // Get the profile data with components
        const data = await response.json();

        // Filter out hidden components and sort by order
        if (data.components && Array.isArray(data.components)) {
          data.components = data.components
            .filter((c: any) =>
              c.hidden !== 'Y' && c.componentType !== 'banner' && c.componentType !== 'profilePicture'
            )
            .sort((a: any, b: any) =>
              parseInt(a.order) - parseInt(b.order)
            );
        }

        setProfileData(data);
      } catch (error) {
        console.error(`Error loading ${featuredProfile} profile data:`, error);
        setError(`An error occurred while loading the ${featuredProfile} profile`);
      } finally {
        setLoading(false);
      }
    }

    fetchProfileData();
  }, [featuredProfile]);

  return (
    <div className="w-full max-w-4xl mx-auto mt-12">
      {loading ? (
        <div className="flex justify-center items-center min-h-[200px]">
          <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
        </div>
      ) : error ? (
        <div className="bg-red-900/20 border border-red-900/50 rounded-lg p-6 text-center">
          <h3 className="text-red-400 font-medium mb-2">Error</h3>
          <p className="text-neutral-300 mb-4">{error}</p>
        </div>
      ) : profileData ? (
        <div className="space-y-0 border border-neutral-700 overflow-hidden">
          {profileData.components.map((component) => {
            if (component.componentType === 'hero') {
              return (
                <div key={`hero-${component.order}`} className="border-b border-neutral-700">
                  <RenderHero
                    address={profileData.address}
                    componentData={{
                      ...component,
                      address: profileData.address,
                      chain: profileData.chain
                    }}
                    showPositionLabel={false}
                  />
                </div>
              );
            } else if (component.componentType === 'socialLinks') {
              return (
                <div key={`socialLinks-${component.order}`} className="border-b border-neutral-700">
                  <RenderSocialLinks
                    profileData={{
                      address: profileData.address,
                      chain: profileData.chain,
                      name: profileData.name || '',
                      bio: profileData.bio || ''
                    }}
                    componentData={{
                      ...component,
                      address: profileData.address,
                      chain: profileData.chain
                    }}
                    showPositionLabel={false}
                  />
                </div>
              );
            } else if (component.componentType === 'bannerpfp') {
              return (
                <div key={`bannerpfp-${component.order}`} className="border-b border-neutral-700">
                  <RenderBannerPfp
                    address={profileData.address}
                    componentData={{
                      ...component,
                      address: profileData.address,
                      chain: profileData.chain
                    }}
                    showPositionLabel={false}
                  />
                </div>
              );
            }
            return null;
          })}
        </div>
      ) : (
        <div className="bg-yellow-900/20 border border-yellow-900/50 rounded-lg p-6 text-center">
          <h3 className="text-yellow-400 font-medium mb-2">Profile Not Found</h3>
          <p className="text-neutral-300 mb-4">
            The featured profile "{featuredProfile}" doesn't exist or has been removed.
          </p>
        </div>
      )}
    </div>
  );
}
