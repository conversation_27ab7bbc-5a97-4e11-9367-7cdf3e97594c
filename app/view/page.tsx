'use client';

import { useState, useEffect } from 'react';
import { useAppKitAccount, useAppKitNetwork } from '@reown/appkit/react';
import { usePathname } from 'next/navigation';
import WalletValidator from '@/components/WalletValidator';
import { toast } from 'sonner';
import { createProfile } from '@/app/actions/profileActions';
// Old render components removed
import RenderHero from '@/app/components/renders/render_hero';
import RenderSocialLinks from '@/app/components/renders/render_sociallinks';
import RenderBannerPfp from '@/app/components/renders/render_bannerpfp';
import { HeaderTypewriterEffect } from '@/components/ui/header-typewriter-effect';

interface ComponentPosition {
  address: string;
  chain: string;
  componentType: string;
  order: string;
  hidden: string;
  imageData?: string;
  scale?: string;
  positionX?: number;
  positionY?: number;
  naturalWidth?: number;
  naturalHeight?: number;
  backgroundColor?: string;
  createdAt: string;
  updatedAt: string;
}

interface ProfileData {
  address: string;
  chain: string;
  name?: string; // Optional for backward compatibility
  profileName?: string; // New field
  bio?: string; // Optional for backward compatibility
  profileBio?: string; // New field
  socialLinks?: any;
  compPosition: 'left' | 'center' | 'right';
  components: ComponentPosition[];
}

export default function ViewPage() {
  const { address, isConnected } = useAppKitAccount();
  const { chainId } = useAppKitNetwork();
  const pathname = usePathname();
  const [isLoading, setIsLoading] = useState(false);
  const [profileData, setProfileData] = useState<ProfileData | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Load profile data when wallet is connected or address changes
  useEffect(() => {
    // Reset states when address changes
    setProfileData(null);
    setError(null);
    const loadProfileData = async () => {
      if (!address) return;

      try {
        setIsLoading(true);
        setError(null);
        // Create profile if it doesn't exist - require chainId
        if (!chainId) {
          setError('Please connect to a supported network');
          return;
        }
        const chain = chainId.toString();
        await createProfile(address, chain);

        // Fetch the profile data
        const response = await fetch(`/api/profile/${address}`);
        // Profile API response received

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Failed to load profile data:', errorText);
          setError('Failed to load profile data');
          return;
        }

        // Get the profile data with components
        const data = await response.json();

        // Filter out deprecated components and sort by order
        if (data.components && Array.isArray(data.components)) {
          data.components = data.components
            .filter((c: ComponentPosition) =>
              c.hidden !== 'Y' && c.componentType !== 'banner' && c.componentType !== 'profilePicture'
            )
            .sort((a: ComponentPosition, b: ComponentPosition) =>
              parseInt(a.order) - parseInt(b.order)
            );
        } else {
          // No components array found
        }

        setProfileData(data);
      } catch (error) {
        console.error('Error loading profile data:', error);
        setError('An error occurred while loading the profile');
      } finally {
        setIsLoading(false);
      }
    };

    if (address) {
      loadProfileData();
    } else {
      setIsLoading(false);
    }
  }, [address, chainId, pathname]);

  return (
    <main className="min-h-screen">
      <div className="container max-w-4xl mx-auto px-4 pt-2 pb-8">
        <div className="text-center mb-2">
          <div className="h-16 sm:h-20 md:h-24 flex items-center justify-center">
            <HeaderTypewriterEffect words={[
              { text: "View" },
              { text: "Your" },
              { text: "Web3" },
              { text: "Profile", className: "text-blue-500 dark:text-blue-500" },
            ]} />
          </div>
          <p className="text-neutral-500 text-sm max-w-lg mx-auto mt-1">
            See how your profile looks to others
          </p>
        </div>
        <WalletValidator>
          {isLoading ? (
            <div className="flex justify-center items-center min-h-[400px]">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : error ? (
            <div className="bg-red-100 dark:bg-red-900/30 border border-red-300 dark:border-red-800 p-6 text-center">
              <h3 className="text-lg font-medium text-red-800 dark:text-red-200 mb-2">{error}</h3>
              <p className="text-red-600 dark:text-red-300 mb-4">
                There was an error loading your profile. Please try again or create your profile in the Create Content page.
              </p>
              <div className="flex flex-col sm:flex-row justify-center gap-4 mt-4">
                <button
                  onClick={() => window.location.reload()}
                  className="px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 transition-colors"
                >
                  Try Again
                </button>
                <button
                  onClick={() => window.location.href = '/createpage'}
                  className="px-4 py-2 bg-red-600 text-white hover:bg-red-700 transition-colors"
                >
                  Go to Create Content
                </button>
              </div>
            </div>
          ) : profileData ? (
            <div className="space-y-0">
              {/* Render components based on their order from the database */}
              {profileData.components && profileData.components.length > 0 ? (
                <div className="space-y-0 border border-neutral-700 overflow-hidden">
                  {profileData.components.map((component: ComponentPosition) => {
                      // Components are already filtered in the data loading phase
                      if (component.componentType === 'socialLinks') {
                        return (
                          <div key={`socialLinks-${component.order}`} className="social-links-container border-b border-neutral-700 last:border-b-0">
                            <RenderSocialLinks
                              profileData={{
                                address: profileData.address,
                                chain: profileData.chain,
                                name: profileData.profileName || profileData.name || '',
                                bio: profileData.profileBio || profileData.bio || ''
                              }}
                              componentData={component}
                            />
                          </div>
                        );
                      } else if (component.componentType === 'hero') {
                        return (
                          <div key={`hero-${component.order}`} className="hero-container border-b border-neutral-700 last:border-b-0">
                            <RenderHero
                              address={address as string}
                              componentData={component}
                            />
                          </div>
                        );
                      } else if (component.componentType === 'bannerpfp') {
                        return (
                          <div key={`bannerpfp-${component.order}`} className="bannerpfp-container border-b border-neutral-700 last:border-b-0">
                            <RenderBannerPfp
                              address={address as string}
                              componentData={component}
                              profileName={profileData.profileName || profileData.name || address?.substring(0, 8) || 'Web3 User'}
                              profileBio={profileData.profileBio || profileData.bio || ''}
                            />
                          </div>
                        );
                      }
                      return null;
                    })}
                </div>
              ) : (
                <div className="bg-yellow-100 dark:bg-yellow-900/30 border border-yellow-300 dark:border-yellow-800 rounded-lg p-6 text-center">
                  <h3 className="text-lg font-medium text-yellow-800 dark:text-yellow-200 mb-2">No Components Found</h3>
                  <p className="text-yellow-600 dark:text-yellow-300 mb-4">
                    Your profile doesn't have any components yet. Go to the Create Content page to add components.
                  </p>
                  <button
                    onClick={() => window.location.href = '/createpage'}
                    className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors"
                  >
                    Go to Create Content
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div className="bg-yellow-100 dark:bg-yellow-900/30 border border-yellow-300 dark:border-yellow-800 rounded-lg p-6 text-center">
              <h3 className="text-lg font-medium text-yellow-800 dark:text-yellow-200 mb-2">No Components Found</h3>
              <p className="text-yellow-600 dark:text-yellow-300 mb-4">
                Your profile doesn't have any components yet. Go to the Create Content page to add components.
              </p>
              <button
                onClick={() => window.location.href = '/createpage'}
                className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors"
              >
                Go to Create Content
              </button>
            </div>
          )}
        </WalletValidator>
      </div>
    </main>
  );
}
