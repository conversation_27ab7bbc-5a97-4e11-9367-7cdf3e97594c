'use client';

import { useState, useEffect, useRef } from 'react';
import { useAppKitAccount, useAppKitNetwork } from '@reown/appkit/react';
import { useRouter } from 'next/navigation';
import { Loader2, Play, Square, Eye, ExternalLink, RefreshCw, Coins, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';

interface User {
  address: string;
  role: string;
  status: string;
  name?: string;
}

interface Transaction {
  hash: string;
  from: string;
  to: string;
  value: string;
  timestamp: number;
  blockNumber: number;
  isTokenCreation?: boolean;
  createdTokenAddress?: string;
  tokenName?: string;
  tokenSymbol?: string;
}

export default function SnipperPage() {
  const { isConnected, address } = useAppKitAccount();
  const { chainId } = useAppKitNetwork();
  const router = useRouter();
  
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const [walletToWatch, setWalletToWatch] = useState('');
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [lastChecked, setLastChecked] = useState<Date | null>(null);
  const [fetchingTransactions, setFetchingTransactions] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Check admin status
  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!isConnected || !address || !chainId) {
        setLoading(false);
        setIsAdmin(false);
        return;
      }

      try {
        console.log('Checking admin status for:', address);
        const response = await fetch(`/api/admin/users/chain/${chainId}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch users');
        }

        const data: User[] = await response.json();
        const currentUser = data.find((user: User) => user.address.toLowerCase() === address.toLowerCase());
        
        if (currentUser && currentUser.role === 'admin') {
          console.log('User is admin, granting access');
          setIsAdmin(true);
        } else {
          console.log('User is not admin or not found');
          setIsAdmin(false);
        }
      } catch (error) {
        console.error('Error checking admin status:', error);
        toast.error('Failed to verify admin status');
        setIsAdmin(false);
      } finally {
        setLoading(false);
      }
    };

    checkAdminStatus();
  }, [isConnected, address, chainId]);

  // Fetch transactions for the watched wallet
  const fetchTransactions = async (walletAddress: string, showLoading = true) => {
    try {
      if (showLoading) setFetchingTransactions(true);

      const response = await fetch(`/api/snipper/transactions?address=${walletAddress}&chainId=${chainId}&limit=5`);

      if (!response.ok) {
        throw new Error('Failed to fetch transactions');
      }

      const data = await response.json();
      const newTransactions = data.transactions || [];

      // Check for token creation transactions
      const tokenCreations = newTransactions.filter((tx: Transaction) => tx.isTokenCreation);

      setTransactions(newTransactions);
      setLastChecked(new Date());

      if (tokenCreations.length > 0) {
        tokenCreations.forEach((tx: Transaction) => {
          toast.success(
            `🚀 NEW TOKEN CREATED! Address: ${tx.createdTokenAddress}`,
            { duration: 10000 }
          );
        });
      } else if (data.totalFound > 0) {
        toast.success(`Found ${data.totalFound} recent transactions`);
      }
    } catch (error) {
      console.error('Error fetching transactions:', error);
      toast.error('Failed to fetch transactions');
    } finally {
      if (showLoading) setFetchingTransactions(false);
    }
  };

  // Start monitoring
  const startMonitoring = async () => {
    if (!walletToWatch.trim()) {
      toast.error('Please enter a wallet address to monitor');
      return;
    }

    if (!walletToWatch.match(/^0x[a-fA-F0-9]{40}$/)) {
      toast.error('Please enter a valid Ethereum wallet address');
      return;
    }

    setIsMonitoring(true);
    toast.success(`Started monitoring wallet: ${walletToWatch}`);
    
    // Initial fetch
    await fetchTransactions(walletToWatch);
    
    // Set up polling every 10 seconds (don't show loading for background fetches)
    intervalRef.current = setInterval(() => {
      fetchTransactions(walletToWatch, false);
    }, 10000);
  };

  // Stop monitoring
  const stopMonitoring = () => {
    setIsMonitoring(false);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    toast.info('Stopped monitoring wallet');
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  // Format address for display
  const formatAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
  };

  // Format timestamp
  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  // Format value (assuming it's in wei)
  const formatValue = (value: string) => {
    const ethValue = parseFloat(value) / Math.pow(10, 18);
    return ethValue.toFixed(6);
  };

  // Get explorer URL based on chain
  const getExplorerUrl = (hash: string) => {
    const chainExplorers: { [key: string]: string } = {
      '25': 'https://cronoscan.com/tx/',
      '1': 'https://etherscan.io/tx/',
      '137': 'https://polygonscan.com/tx/',
      '56': 'https://bscscan.com/tx/',
      '42161': 'https://arbiscan.io/tx/',
    };

    const baseUrl = chainExplorers[chainId?.toString() || '25'] || 'https://cronoscan.com/tx/';
    return `${baseUrl}${hash}`;
  };

  // Handle buying a newly created token
  const handleBuyToken = async (tokenAddress: string) => {
    if (!address) {
      toast.error('Please connect your wallet first');
      return;
    }

    try {
      const buyAmount = '10'; // Default 10 CRO - you can make this configurable

      toast.info(`Preparing to buy token ${formatAddress(tokenAddress)} with ${buyAmount} CRO...`);

      const response = await fetch('/api/snipper/buy-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tokenAddress,
          amount: buyAmount,
          walletAddress: address
        })
      });

      const data = await response.json();

      if (data.success) {
        if (data.placeholder) {
          toast.info('Buy token feature ready - DEX integration needed');
          console.log('Buy token instructions:', data.instructions);
        } else {
          toast.success(`Token purchase successful! TX: ${data.transactionHash}`);
        }
      } else {
        toast.error(`Failed to buy token: ${data.error}`);
      }
    } catch (error) {
      console.error('Error buying token:', error);
      toast.error('Failed to buy token');
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-4">Snipper Dashboard</h1>
        <div className="flex items-center gap-2">
          <Loader2 className="h-4 w-4 animate-spin" />
          <p>Loading snipper dashboard...</p>
        </div>
        <div className="mt-4 text-sm text-gray-600">
          <p>Connected: {String(isConnected)}</p>
          <p>Address: {address || 'None'}</p>
          <p>Network: {chainId ? `Chain ${chainId}` : 'Not connected'}</p>
        </div>
      </div>
    );
  }

  // Show connection required message
  if (!isConnected || !address || !chainId) {
    return (
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-4">Snipper Dashboard</h1>
        <p>Please connect your wallet and ensure you're on a supported network to access the snipper page.</p>
        <div className="mt-4 text-sm text-gray-600">
          <p>Connected: {String(isConnected)}</p>
          <p>Address: {address ? 'Yes' : 'No'}</p>
          <p>Network: {chainId ? `Chain ${chainId}` : 'Not connected'}</p>
        </div>
      </div>
    );
  }

  // Show access denied message
  if (!isAdmin) {
    return (
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-4">Snipper Dashboard</h1>
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <p className="text-red-800 dark:text-red-200">You do not have permission to access this page.</p>
          <p className="text-sm text-red-600 dark:text-red-400 mt-2">
            Connected as: {address}
          </p>
          <p className="text-sm text-red-600 dark:text-red-400">
            Only admin users can access the snipper dashboard.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">Snipper Dashboard</h1>
      
      {/* Wallet Input Section */}
      <div className="bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 rounded-lg p-6 mb-6">
        <h2 className="text-lg font-semibold mb-4">Wallet Monitor</h2>
        
        <div className="flex gap-4 items-end">
          <div className="flex-1">
            <label htmlFor="walletAddress" className="block text-sm font-medium mb-2">
              Wallet Address to Monitor
            </label>
            <input
              id="walletAddress"
              type="text"
              value={walletToWatch}
              onChange={(e) => setWalletToWatch(e.target.value)}
              placeholder="0x..."
              disabled={isMonitoring}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md 
                         bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100
                         focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                         disabled:opacity-50 disabled:cursor-not-allowed"
            />
          </div>
          
          <div className="flex gap-2">
            {!isMonitoring ? (
              <button
                onClick={startMonitoring}
                className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md 
                           flex items-center gap-2 transition-colors"
              >
                <Play className="h-4 w-4" />
                Start
              </button>
            ) : (
              <button
                onClick={stopMonitoring}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md 
                           flex items-center gap-2 transition-colors"
              >
                <Square className="h-4 w-4" />
                Stop
              </button>
            )}
          </div>
        </div>
        
        {/* Status Indicator */}
        {isMonitoring && (
          <div className="mt-4 flex items-center gap-2 text-sm">
            <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-green-600 dark:text-green-400">
              Monitoring {formatAddress(walletToWatch)}
            </span>
            {lastChecked && (
              <span className="text-gray-500 ml-4">
                Last checked: {lastChecked.toLocaleTimeString()}
              </span>
            )}
          </div>
        )}
      </div>

      {/* Transactions Table */}
      <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-800 flex items-center justify-between">
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Recent Transactions ({transactions.length})
          </h2>

          {walletToWatch && (
            <button
              onClick={() => fetchTransactions(walletToWatch)}
              disabled={fetchingTransactions}
              className="px-3 py-1.5 text-sm bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400
                         text-white rounded-md flex items-center gap-2 transition-colors"
            >
              <RefreshCw className={`h-4 w-4 ${fetchingTransactions ? 'animate-spin' : ''}`} />
              {fetchingTransactions ? 'Refreshing...' : 'Refresh'}
            </button>
          )}
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Hash
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  From
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  To
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Value (CRO)
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Time
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Block
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {transactions.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-6 py-8 text-center text-gray-500 dark:text-gray-400">
                    {isMonitoring ? 'No transactions found yet...' : 'Start monitoring a wallet to see transactions'}
                  </td>
                </tr>
              ) : (
                transactions.map((tx) => (
                  <tr key={tx.hash} className={`hover:bg-gray-50 dark:hover:bg-gray-800 ${tx.isTokenCreation ? 'bg-green-50 dark:bg-green-900/20 border-l-4 border-green-500' : ''}`}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-2">
                        {tx.isTokenCreation ? (
                          <div className="flex items-center gap-1 text-green-600 dark:text-green-400">
                            <Coins className="h-4 w-4" />
                            <span className="text-xs font-semibold">TOKEN</span>
                          </div>
                        ) : (
                          <div className="flex items-center gap-1 text-gray-500">
                            <AlertCircle className="h-4 w-4" />
                            <span className="text-xs">TX</span>
                          </div>
                        )}
                      </div>
                      {tx.isTokenCreation && tx.createdTokenAddress && (
                        <div className="mt-1">
                          <div className="text-xs text-green-600 dark:text-green-400 font-mono">
                            {formatAddress(tx.createdTokenAddress)}
                          </div>
                          <div className="flex gap-2">
                            <button
                              onClick={() => {
                                navigator.clipboard.writeText(tx.createdTokenAddress!);
                                toast.success('Token address copied!');
                              }}
                              className="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 underline"
                            >
                              Copy Address
                            </button>
                            <button
                              onClick={() => handleBuyToken(tx.createdTokenAddress!)}
                              className="text-xs bg-green-600 hover:bg-green-700 text-white px-2 py-1 rounded"
                            >
                              Buy Token
                            </button>
                          </div>
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-2">
                        <span className="font-mono text-sm">{formatAddress(tx.hash)}</span>
                        <a
                          href={getExplorerUrl(tx.hash)}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                        >
                          <ExternalLink className="h-3 w-3" />
                        </a>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap font-mono text-sm">
                      {formatAddress(tx.from)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap font-mono text-sm">
                      {formatAddress(tx.to)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      {formatValue(tx.value)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {formatTimestamp(tx.timestamp)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {tx.blockNumber}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
