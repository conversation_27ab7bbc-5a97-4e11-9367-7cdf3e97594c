import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { web3Profile, componentPositions } from '@/db/schema';
import { eq } from 'drizzle-orm';

export async function GET(request: NextRequest): Promise<Response> {
  try {
    const { searchParams } = new URL(request.url);
    const profileName = searchParams.get('name');
    const walletAddress = searchParams.get('walletAddress');

    if (!profileName) {
      console.log('Profile name is missing');
      return Response.json(
        { error: 'Profile name is required' },
        { status: 400 }
      );
    }

    console.log(`Checking ownership for profile: ${profileName} with wallet: ${walletAddress || 'not connected'}`);

    if (!walletAddress) {
      console.log('Wallet address is missing');
      return Response.json(
        { isOwner: false, message: 'Wallet not connected' },
        { status: 200 }
      );
    }

    // First, try to find profile by address if the profileName looks like an address
    if (profileName.startsWith('0x')) {
      console.log(`Profile name looks like an address, trying direct address lookup: ${profileName}`);
      const profileByAddress = await db
        .select({
          address: web3Profile.address,
          name: web3Profile.name,
          status: web3Profile.status
        })
        .from(web3Profile)
        .where(eq(web3Profile.address, profileName));

      console.log(`Profile lookup by address results:`, profileByAddress);

      if (profileByAddress.length > 0) {
        const isOwner = profileByAddress[0].address.toLowerCase() === walletAddress.toLowerCase();
        console.log(`Profile found by address. Is owner: ${isOwner}`);
        return Response.json({
          isOwner,
          profileStatus: profileByAddress[0].status,
          message: isOwner ? 'You are the owner of this profile' : 'You are not the owner of this profile'
        });
      }
    }

    // Try to find profile by name in web3Profile table
    const profile = await db
      .select({
        address: web3Profile.address,
        name: web3Profile.name,
        status: web3Profile.status
      })
      .from(web3Profile)
      .where(eq(web3Profile.name, profileName));

    console.log(`Profile lookup by name '${profileName}' results:`, profile);

    if (profile.length > 0) {
      const isOwner = profile[0].address.toLowerCase() === walletAddress.toLowerCase();
      console.log(`Profile found by name. Profile address: ${profile[0].address}, Wallet address: ${walletAddress}`);
      console.log(`Is owner: ${isOwner}`);

      return Response.json({
        isOwner,
        profileStatus: profile[0].status,
        message: isOwner ? 'You are the owner of this profile' : 'You are not the owner of this profile'
      });
    }

    // If not found by name, try to find by searching in bannerpfp component details
    // This handles cases where the profileName is actually a display name
    console.log(`Profile not found by name, searching in bannerpfp components for display name: ${profileName}`);

    const bannerpfpComponents = await db
      .select()
      .from(componentPositions)
      .where(eq(componentPositions.componentType, 'bannerpfp'));

    console.log(`Found ${bannerpfpComponents.length} bannerpfp components to search`);

    for (const component of bannerpfpComponents) {
      const details = component.details as any;
      if (details && details.profileName === profileName) {
        console.log(`Found matching profileName in component for address: ${component.address}`);

        // Get the profile for this address
        const matchingProfile = await db
          .select({
            address: web3Profile.address,
            name: web3Profile.name,
            status: web3Profile.status
          })
          .from(web3Profile)
          .where(eq(web3Profile.address, component.address));

        if (matchingProfile.length > 0) {
          const isOwner = matchingProfile[0].address.toLowerCase() === walletAddress.toLowerCase();
          console.log(`Profile found by component match. Profile address: ${matchingProfile[0].address}, Wallet address: ${walletAddress}`);
          console.log(`Is owner: ${isOwner}`);

          return Response.json({
            isOwner,
            profileStatus: matchingProfile[0].status,
            message: isOwner ? 'You are the owner of this profile' : 'You are not the owner of this profile'
          });
        }
      }
    }

    // If still not found, return not found
    console.log(`Profile not found: ${profileName}`);
    return Response.json(
      { isOwner: false, message: 'Profile not found' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Failed to check profile ownership:', error);
    return Response.json(
      { error: 'Failed to check profile ownership' },
      { status: 500 }
    );
  }
}
