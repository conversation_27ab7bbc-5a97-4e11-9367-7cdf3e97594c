import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { web3Profile, componentPositions } from '@/db/schema';
import { eq, or, and } from 'drizzle-orm';

export async function GET(request: NextRequest): Promise<Response> {
  try {
    const { searchParams } = new URL(request.url);
    const identifier = searchParams.get('identifier');

    if (!identifier) {
      return Response.json(
        { error: 'Profile identifier (address or name) is required' },
        { status: 400 }
      );
    }

    // Check if the input is an Ethereum address (0x...)
    const isAddress = identifier.startsWith('0x') && identifier.length >= 40;

    // Define profile type based on the select query
    type ProfileType = {
      address: string;
      name: string | null;
      status: string;
      expiryDate: Date | null;
      transactionHash: string | null;
    };

    let profile: ProfileType[] = [];
    let matchingComponents: any[] = [];
    let bannerpfpComponents: any[] = [];

    if (isAddress) {
      // If it's an address, get profile data


      profile = await db
        .select({
          address: web3Profile.address,
          name: web3Profile.name,
          status: web3Profile.status,
          expiryDate: web3Profile.expiryDate,
          transactionHash: web3Profile.transactionHash
        })
        .from(web3Profile)
        .where(eq(web3Profile.address, identifier));

      if (profile.length === 0) {
        // If not found by address, try looking up by name as a fallback


        // First try to find a profile with this name in the web3Profile table
        const profilesByName = await db
          .select({
            address: web3Profile.address,
            name: web3Profile.name,
            status: web3Profile.status,
            expiryDate: web3Profile.expiryDate,
            transactionHash: web3Profile.transactionHash
          })
          .from(web3Profile)
          .where(eq(web3Profile.name, identifier));

        if (profilesByName.length > 0) {

          profile = profilesByName;
        } else {
          // Try to find by name in bannerpfp components
          await searchInBannerpfpComponents(identifier);
        }
      }
    } else {
      // If it's a name, look up the profile by name


      // First try to find a profile with this name in the web3Profile table
      const profilesByName = await db
        .select({
          address: web3Profile.address,
          name: web3Profile.name,
          status: web3Profile.status,
          expiryDate: web3Profile.expiryDate,
          transactionHash: web3Profile.transactionHash
        })
        .from(web3Profile)
        .where(eq(web3Profile.name, identifier));

      if (profilesByName.length > 0) {

        profile = profilesByName;
      } else {
        // Try to find by name in bannerpfp components
        await searchInBannerpfpComponents(identifier);
      }
    }

    // Helper function to search in bannerpfp components
    async function searchInBannerpfpComponents(searchName: string) {


      // Find all bannerpfp components
      bannerpfpComponents = await db
        .select()
        .from(componentPositions)
        .where(eq(componentPositions.componentType, 'bannerpfp'));

      // Filter components by profileName or urlName in the details field
      // Use case-insensitive comparison and trim whitespace
      const searchTerm = searchName.trim().toLowerCase();


      // Try exact match on profileName or urlName
      matchingComponents = bannerpfpComponents.filter(comp => {
        const details = comp.details || {};
        const storedName = details.profileName?.trim().toLowerCase();
        const storedUrlName = details.urlName?.trim().toLowerCase();

        return storedName === searchTerm || storedUrlName === searchTerm;
      });

      // If we found matching components, get the profile by address
      if (matchingComponents.length > 0) {
        const matchingAddress = matchingComponents[0].address;


        profile = await db
          .select({
            address: web3Profile.address,
            name: web3Profile.name,
            status: web3Profile.status,
            expiryDate: web3Profile.expiryDate,
            transactionHash: web3Profile.transactionHash
          })
          .from(web3Profile)
          .where(eq(web3Profile.address, matchingAddress));

        if (profile.length > 0) {

        }
      } else {

      }
    }

    if (profile.length === 0) {
      return Response.json(
        {
          status: 'not-found',
          isApproved: false,
          message: 'Profile not found'
        },
        { status: 200 }
      );
    }

    const userProfile = profile[0];

    // Check if profile has expired
    const isExpired = userProfile.expiryDate && new Date(userProfile.expiryDate) < new Date();

    // If profile has expired, return 'expired' status
    if (isExpired) {
      // Get the profile name from componentPositions if available
      const bannerpfpComponent = await db
        .select()
        .from(componentPositions)
        .where(
          and(
            eq(componentPositions.address, userProfile.address),
            eq(componentPositions.componentType, 'bannerpfp')
          )
        );

      // Get the profile name from componentPositions details field
      const profileName = bannerpfpComponent.length > 0 && bannerpfpComponent[0].details
        ? (bannerpfpComponent[0].details as any).profileName || null
        : null;

      // Log if we're missing profile name
      if (!profileName) {
        console.warn(`Missing profileName for address: ${userProfile.address}`);
      }

      return Response.json(
        {
          status: 'expired',
          isApproved: false,
          message: 'Profile has expired',
          address: userProfile.address,
          name: profileName, // Use the profile name from componentPositions
          expiryDate: userProfile.expiryDate,
          transactionHash: userProfile.transactionHash
        },
        { status: 200 }
      );
    }

    // Log if we're missing profile name in web3Profile
    if (!userProfile.name) {
      console.warn(`Missing name in web3Profile for address: ${userProfile.address}`);
    }

    // Return profile status with address for client-side ownership check
    return Response.json(
      {
        status: userProfile.status,
        isApproved: userProfile.status === 'approved',
        message: getStatusMessage(userProfile.status),
        address: userProfile.address,
        name: userProfile.name, // Use the name from web3Profile table
        expiryDate: userProfile.expiryDate,
        transactionHash: userProfile.transactionHash
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Failed to fetch profile status:', error);
    return Response.json(
      { error: 'Failed to fetch profile status' },
      { status: 500 }
    );
  }
}

/**
 * Get a user-friendly message based on profile status
 */
function getStatusMessage(status: string): string {
  switch (status) {
    case 'approved':
      return 'Profile is approved and accessible.';
    case 'new':
      return 'This profile is new and waiting for approval. Please burn tokens and provide a transaction hash for verification.';
    case 'in-progress':
      return 'This profile is being processed. Please check back later.';
    case 'pending':
      return 'This profile is pending approval. Please check back later.';
    case 'deleted':
      return 'This profile has been deleted.';
    default:
      return 'Profile status is unknown.';
  }
}
