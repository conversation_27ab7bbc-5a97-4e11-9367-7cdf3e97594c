import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { componentPositions } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

type Context = {
  params: Promise<{
    address: string;
  }>;
};

export async function POST(
  request: NextRequest,
  context: Context
): Promise<Response> {
  try {
    const { address } = await context.params;

    if (!address) {
      return Response.json(
        { error: 'Address is required' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { componentType, hidden, chain } = body;

    if (!componentType) {
      return Response.json(
        { error: 'Component type is required' },
        { status: 400 }
      );
    }

    if (hidden !== 'Y' && hidden !== 'N') {
      return Response.json(
        { error: 'Hidden must be Y or N' },
        { status: 400 }
      );
    }

    // First check if the component exists
    const existingComponent = await db
      .select()
      .from(componentPositions)
      .where(
        and(
          eq(componentPositions.address, address),
          eq(componentPositions.componentType, componentType),
          eq(componentPositions.chain, chain)
        )
      );

    if (existingComponent.length === 0) {
      return Response.json(
        { error: 'Component not found' },
        { status: 404 }
      );
    }

    // Update the component visibility
    await db
      .update(componentPositions)
      .set({
        hidden,
        updatedAt: new Date()
      })
      .where(
        and(
          eq(componentPositions.address, address),
          eq(componentPositions.componentType, componentType),
          eq(componentPositions.chain, chain)
        )
      );

    // Get the updated component
    const updatedComponent = await db
      .select()
      .from(componentPositions)
      .where(
        and(
          eq(componentPositions.address, address),
          eq(componentPositions.componentType, componentType),
          eq(componentPositions.chain, chain)
        )
      );

    return Response.json({
      message: 'Component visibility updated successfully',
      component: updatedComponent[0]
    });
  } catch (error) {
    console.error('Failed to update component visibility:', error);
    return Response.json(
      { error: 'Failed to update component visibility' },
      { status: 500 }
    );
  }
}

