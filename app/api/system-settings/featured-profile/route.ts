import { NextRequest } from 'next/server';
import { getFeaturedProfileName } from '@/app/utils/systemSettings';

export async function GET(request: NextRequest): Promise<Response> {
  try {
    const { searchParams } = new URL(request.url);
    const chain = searchParams.get('chain') || '25'; // Default to Cronos

    const featuredProfile = await getFeaturedProfileName(chain);

    return Response.json({
      featuredProfile,
      chain
    });
  } catch (error) {
    console.error('Error getting featured profile:', error);
    return Response.json(
      { error: 'Failed to get featured profile' },
      { status: 500 }
    );
  }
}
