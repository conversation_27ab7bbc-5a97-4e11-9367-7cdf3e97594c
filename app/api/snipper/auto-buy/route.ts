import { NextRequest } from 'next/server';
import { createWalletClient, http, parseEther, formatUnits, createPublicClient } from 'viem';
import { privateKeyToAccount } from 'viem/accounts';
import { getDexConfig, isChainSupported } from '../../../../lib/dexConfig';

// DEX Router ABI (Uniswap V2 style) - minimal for swapExactETHForTokens
const DEX_ROUTER_ABI = [
  {
    name: 'swapExactETHForTokens',
    type: 'function',
    inputs: [
      { name: 'amountOutMin', type: 'uint256' },
      { name: 'path', type: 'address[]' },
      { name: 'to', type: 'address' },
      { name: 'deadline', type: 'uint256' }
    ],
    outputs: [{ name: 'amounts', type: 'uint256[]' }],
    stateMutability: 'payable'
  },
  {
    name: 'getAmountsOut',
    type: 'function',
    inputs: [
      { name: 'amountIn', type: 'uint256' },
      { name: 'path', type: 'address[]' }
    ],
    outputs: [{ name: 'amounts', type: 'uint256[]' }],
    stateMutability: 'view'
  }
] as const;



export async function POST(request: NextRequest): Promise<Response> {
  try {
    const { tokenAddress, amount, slippage, privateKey, chainId } = await request.json();

    // Validate required parameters
    if (!tokenAddress || !amount || !privateKey || !chainId) {
      return Response.json(
        { error: 'Token address, amount, private key, and chain ID are required' },
        { status: 400 }
      );
    }

    // Validate token address format
    if (!tokenAddress.match(/^0x[a-fA-F0-9]{40}$/)) {
      return Response.json(
        { error: 'Invalid token address format' },
        { status: 400 }
      );
    }

    // Validate private key format
    if (!privateKey.match(/^0x[a-fA-F0-9]{64}$/)) {
      return Response.json(
        { error: 'Invalid private key format' },
        { status: 400 }
      );
    }

    // Check if chain is supported
    if (!isChainSupported(chainId)) {
      return Response.json(
        { error: `Unsupported chain ID: ${chainId}` },
        { status: 400 }
      );
    }

    // Get chain configuration
    const chainConfig = getDexConfig(chainId)!;

    console.log(`Auto-buy request: ${amount} native tokens for ${tokenAddress} on chain ${chainId}`);

    // Create account from private key
    const account = privateKeyToAccount(privateKey as `0x${string}`);
    
    // Create wallet client
    const walletClient = createWalletClient({
      account,
      chain: chainConfig.chain,
      transport: http(chainConfig.rpcUrl)
    });

    // Create public client for reading data
    const publicClient = createPublicClient({
      chain: chainConfig.chain,
      transport: http(chainConfig.rpcUrl)
    });

    // Convert amount to wei
    const amountInWei = parseEther(amount);
    
    // Set up swap path: Native -> Token
    const path = [chainConfig.wrappedNative, tokenAddress];
    
    // Calculate minimum tokens out with slippage
    const slippagePercent = parseFloat(slippage || '5');
    
    try {
      // Get expected amounts out
      const amountsOut = await publicClient.readContract({
        address: chainConfig.dexRouter as `0x${string}`,
        abi: DEX_ROUTER_ABI,
        functionName: 'getAmountsOut',
        args: [amountInWei, path as `0x${string}`[]]
      });

      const expectedTokensOut = amountsOut[1];
      const minTokensOut = (expectedTokensOut * BigInt(Math.floor((100 - slippagePercent) * 100))) / BigInt(10000);

      console.log(`Expected tokens out: ${formatUnits(expectedTokensOut, 18)}`);
      console.log(`Min tokens out (${slippagePercent}% slippage): ${formatUnits(minTokensOut, 18)}`);

      // Set deadline (10 minutes from now)
      const deadline = BigInt(Math.floor(Date.now() / 1000) + 600);

      // Execute the swap
      const txHash = await walletClient.writeContract({
        address: chainConfig.dexRouter as `0x${string}`,
        abi: DEX_ROUTER_ABI,
        functionName: 'swapExactETHForTokens',
        args: [minTokensOut, path as `0x${string}`[], account.address, deadline],
        value: amountInWei,
        gas: BigInt(300000), // Set gas limit
        gasPrice: BigInt(chainConfig.gasPrice)
      });

      console.log(`Auto-buy transaction submitted: ${txHash}`);

      // Wait for transaction confirmation
      const receipt = await publicClient.waitForTransactionReceipt({
        hash: txHash,
        timeout: 60000 // 60 second timeout
      });

      if (receipt.status === 'success') {
        return Response.json({
          success: true,
          message: 'Auto-buy completed successfully',
          transactionHash: txHash,
          tokenAddress,
          amountIn: amount,
          expectedTokensOut: formatUnits(expectedTokensOut, 18),
          minTokensOut: formatUnits(minTokensOut, 18),
          slippage: slippagePercent,
          gasUsed: receipt.gasUsed.toString(),
          blockNumber: receipt.blockNumber.toString()
        });
      } else {
        return Response.json({
          success: false,
          error: 'Transaction failed',
          transactionHash: txHash
        }, { status: 500 });
      }

    } catch (swapError) {
      console.error('Swap execution error:', swapError);
      
      // Check if it's a liquidity/pair issue
      if (swapError instanceof Error && swapError.message.includes('INSUFFICIENT_OUTPUT_AMOUNT')) {
        return Response.json({
          success: false,
          error: 'Insufficient liquidity or high slippage. Try increasing slippage tolerance.',
          details: swapError.message
        }, { status: 400 });
      }
      
      if (swapError instanceof Error && swapError.message.includes('INSUFFICIENT_INPUT_AMOUNT')) {
        return Response.json({
          success: false,
          error: 'Insufficient input amount. Try increasing buy amount.',
          details: swapError.message
        }, { status: 400 });
      }

      return Response.json({
        success: false,
        error: 'Failed to execute swap',
        details: swapError instanceof Error ? swapError.message : 'Unknown error'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Auto-buy error:', error);
    
    let errorMessage = 'Failed to process auto-buy request';
    if (error instanceof Error) {
      errorMessage = error.message;
    }

    return Response.json(
      { 
        error: errorMessage,
        success: false 
      },
      { status: 500 }
    );
  }
}
