'use server';

import { db } from '@/db/drizzle';
import { systemSettings } from '@/db/schema';
import { eq, sql } from 'drizzle-orm';

interface ComponentDefault {
  componentType: string;
  order: string;
  hidden: string;
  details?: {
    backgroundColor?: string;
    fontColor?: string | null;
    shape?: string;
    profileName?: string;
    profileBio?: string;
    defaultImagePath?: string;
    defaultBannerImagePath?: string;
    defaultProfileImagePath?: string;
    profileShape?: string;
    profileHorizontalPosition?: number;
    profileNameHorizontalPosition?: number;
    profileNameStyle?: {
      fontSize?: string;
      fontWeight?: string;
      fontColor?: string;
      effect?: string;
    };
    heroContent?: Array<{
      title: string;
      description: string;
      contentType: string;
      colorGradient?: string;
    }>;
    socialLinks?: {
      twitter?: string;
      discord?: string;
      telegram?: string;
      website?: string;
      facebook?: string;
      youtube?: string;
      email?: string;
      linkedin?: string;
      cro?: string;
    };
  };
}

interface ComponentDefaults {
  defaults: ComponentDefault[];
}

/**
 * Get component defaults from system settings for a specific chain
 * @param chainId - The chain ID to get component defaults for
 * @returns Array of default component settings
 */
export async function getComponentDefaults(chainId: string): Promise<ComponentDefault[]> {
  try {
    // Get chain-specific component defaults from system settings
    const settingId = `component_defaults_${chainId}`;
    const settings = await db
      .select()
      .from(systemSettings)
      .where(eq(systemSettings.id, settingId));

    if (settings.length === 0) {
      console.log(`No component defaults found for chain ${chainId}, returning fallback component defaults`);
      // Return fallback default components to ensure profiles have basic components
      return [
        {
          componentType: 'bannerpfp',
          order: '1',
          hidden: 'N',
          details: {
            backgroundColor: 'transparent',
            fontColor: null,
            profileShape: 'circular',
            profileHorizontalPosition: 20,
            profileNameHorizontalPosition: 50,
            profileNameStyle: {
              fontSize: '1.5rem',
              fontWeight: 'bold',
              fontColor: '#ffffff',
              effect: 'typewriter'
            },
            defaultBannerImagePath: 'banner.png',
            defaultProfileImagePath: 'pfp.jpg'
          }
        },
        {
          componentType: 'socialLinks',
          order: '2',
          hidden: 'N',
          details: {
            backgroundColor: 'transparent',
            fontColor: null,
            socialLinks: {
              twitter: '',
              discord: '',
              telegram: '',
              website: '',
              facebook: '',
              youtube: '',
              email: '',
              linkedin: '',
              cro: ''
            }
          }
        }
      ];
    }

    // Parse component defaults - the script stores them directly under 'defaults'
    const componentDefaults = settings[0].value as any;
    if (componentDefaults.defaults && Array.isArray(componentDefaults.defaults)) {
      return componentDefaults.defaults;
    }

    console.log(`No component defaults found for chain ${chainId}, returning fallback component defaults`);
    // Return fallback default components to ensure profiles have basic components
    return [
      {
        componentType: 'bannerpfp',
        order: '1',
        hidden: 'N',
        details: {
          backgroundColor: 'transparent',
          fontColor: null,
          profileShape: 'circular',
          profileHorizontalPosition: 20,
          profileNameHorizontalPosition: 50,
          profileNameStyle: {
            fontSize: '1.5rem',
            fontWeight: 'bold',
            fontColor: '#ffffff',
            effect: 'typewriter'
          },
          defaultBannerImagePath: 'banner.png',
          defaultProfileImagePath: 'pfp.jpg'
        }
      },
      {
        componentType: 'socialLinks',
        order: '2',
        hidden: 'N',
        details: {
          backgroundColor: 'transparent',
          fontColor: null,
          socialLinks: {
            twitter: '',
            discord: '',
            telegram: '',
            website: '',
            facebook: '',
            youtube: '',
            email: '',
            linkedin: '',
            cro: ''
          }
        }
      }
    ];
  } catch (error) {
    console.error('Error getting component defaults:', error);
    throw error;
  }
}

interface ChainConfig {
  id: string;
  name: string;
}

interface PartnerToken {
  id: string;
  tokenAddress: string;
  tokenName: string;
  minimumHoldings: string;
}

interface TokenRequirements {
  mainRequirement?: {
    chainId: string;
    chainName: string;
    tokenAddress: string;
    tokenName: string;
    minimumHoldings: string;
    secondaryMinHoldings?: string;
  };
  secondaryRequirements?: Array<{
    id: string;
    tokenAddress: string;
    tokenName: string;
    minimumHoldings: string;
  }>;
  // Legacy fields for backward compatibility
  selectedChain?: string;
  chainConfigs?: ChainConfig[];
  partnerTokens?: Record<string, PartnerToken[]>;
  tokenAddresses?: Record<string, string>;
  tokenNames?: Record<string, string>;
  minimumHoldings?: Record<string, string>;
}

/**
 * Get token requirements from system settings for a specific chain
 * @param chainId - The chain ID to get token requirements for
 * @returns Token requirements object
 */
export async function getTokenRequirements(chainId: string): Promise<TokenRequirements> {
  try {
    // Get chain-specific token requirements from system settings
    const settingId = `token_requirements_${chainId}`;
    console.log('=== getTokenRequirements DEBUG ===');
    console.log('Looking for setting ID:', settingId);

    const settings = await db
      .select()
      .from(systemSettings)
      .where(eq(systemSettings.id, settingId));

    console.log('Raw database result:', JSON.stringify(settings, null, 2));

    if (settings.length === 0) {
      console.log(`No token requirements found for chain ${chainId}, returning default token requirements`);
      // Return default token requirements structure
      return {
        mainRequirement: {
          chainId,
          chainName: '',
          tokenAddress: '',
          tokenName: 'Web3Tools',
          minimumHoldings: '',
          secondaryMinHoldings: ''
        },
        secondaryRequirements: []
      };
    }

    // Parse token requirements - the script stores them directly as the value
    const tokenRequirements = settings[0].value as any;
    console.log('Parsed token requirements:', JSON.stringify(tokenRequirements, null, 2));

    if (tokenRequirements && typeof tokenRequirements === 'object') {
      console.log('Returning token requirements:', JSON.stringify(tokenRequirements, null, 2));
      return tokenRequirements;
    }

    // Return default if no token requirements found
    return {
      mainRequirement: {
        chainId,
        chainName: '',
        tokenAddress: '',
        tokenName: 'Web3Tools',
        minimumHoldings: '',
        secondaryMinHoldings: ''
      },
      secondaryRequirements: []
    };
  } catch (error) {
    console.error('Error getting token requirements:', error);
    throw error;
  }
}

/**
 * Get token requirements for a specific chain
 * @param chainId The chain ID to get requirements for
 * @returns Object containing token requirements for the specified chain
 */
export async function getTokenRequirementsForChain(chainId: string): Promise<{
  tokenAddress: string;
  tokenName: string;
  minimumHoldings: string;
  secondaryMinHoldings?: string;
}> {
  try {
    const allRequirements = await getTokenRequirements(chainId);

    // Handle new chain-specific structure
    if (allRequirements.mainRequirement) {
      return {
        tokenAddress: allRequirements.mainRequirement.tokenAddress || '',
        tokenName: allRequirements.mainRequirement.tokenName || 'Web3Tools',
        minimumHoldings: allRequirements.mainRequirement.minimumHoldings || '0',
        secondaryMinHoldings: allRequirements.mainRequirement.secondaryMinHoldings || '0'
      };
    }

    // Fallback to legacy structure for backward compatibility
    if (allRequirements.tokenAddresses) {
      const tokenAddress = allRequirements.tokenAddresses[chainId] || '';
      const tokenName = allRequirements.tokenNames?.[chainId] || 'Web3Tools';
      const minimumHoldings = allRequirements.minimumHoldings?.[chainId] || '0';

      return {
        tokenAddress,
        tokenName,
        minimumHoldings,
        secondaryMinHoldings: '0'
      };
    }

    // Return empty values if no configuration found
    return {
      tokenAddress: '',
      tokenName: 'Web3Tools',
      minimumHoldings: '0',
      secondaryMinHoldings: '0'
    };
  } catch (error) {
    console.error(`Error getting token requirements for chain ${chainId}:`, error);
    throw error;
  }
}

interface ProfileDefaults {
  default_role: string;
  default_status: string;
  default_expiry_days: string;
  default_profile_name_format: string;
  default_profile_bio: string;
}

/**
 * Initialize default profile settings in the database
 * @returns True if successful, false otherwise
 */
export async function initializeProfileDefaults(): Promise<boolean> {
  try {
    console.log('Initializing default profile settings...');

    // Default profile settings
    const defaultProfileSettings: ProfileDefaults = {
      default_role: 'user',
      default_status: 'new',
      default_expiry_days: '1',
      default_profile_name_format: 'Web3 User {address}',
      default_profile_bio: 'Welcome to my Web3 Social profile!'
    };

    // Insert or update profile defaults in system_settings table
    await db.insert(systemSettings).values({
      id: 'profile_defaults',
      value: defaultProfileSettings,
      createdAt: new Date(),
      updatedAt: new Date()
    }).onDuplicateKeyUpdate({ set: {
      value: sql`${JSON.stringify(defaultProfileSettings)}`,
      updatedAt: new Date()
    }});

    // Default profile settings initialized
    return true;
  } catch (error) {
    console.error('Error initializing default profile settings:', error);
    return false;
  }
}

/**
 * Get profile defaults from system settings for a specific chain
 * @param chainId - The chain ID to get profile defaults for
 * @returns Profile defaults object
 */
export async function getProfileDefaults(chainId: string): Promise<ProfileDefaults> {
  try {
    // Get chain-specific profile defaults from system settings
    const settingId = `profile_defaults_${chainId}`;
    const settings = await db
      .select()
      .from(systemSettings)
      .where(eq(systemSettings.id, settingId));

    if (settings.length === 0) {
      console.log(`No profile defaults found for chain ${chainId}, returning default profile defaults`);
      // Return default profile defaults
      return {
        default_role: 'user',
        default_status: 'new',
        default_expiry_days: '30',
        default_profile_name_format: 'Web3 User {address}',
        default_profile_bio: 'Welcome to my Web3 Social profile!'
      };
    }

    // Parse profile defaults - the script stores them directly as the value
    const profileDefaults = settings[0].value as any;
    if (profileDefaults && typeof profileDefaults === 'object') {
      return profileDefaults;
    }

    // Return default if no profile defaults found
    return {
      default_role: 'user',
      default_status: 'new',
      default_expiry_days: '30',
      default_profile_name_format: 'Web3 User {address}',
      default_profile_bio: 'Welcome to my Web3 Social profile!'
    };
  } catch (error) {
    console.error('Error getting profile defaults:', error);
    throw error;
  }
}

/**
 * Get featured profile name from system settings for a specific chain
 * @param chainId - The chain ID to get featured profile for
 * @returns Featured profile name
 */
export async function getFeaturedProfileName(chainId: string): Promise<string> {
  try {
    // Get chain-specific featured profile from system settings
    const settingId = `featured_profile_${chainId}`;
    const settings = await db
      .select()
      .from(systemSettings)
      .where(eq(systemSettings.id, settingId));

    if (settings.length === 0) {
      console.log(`No featured profile found for chain ${chainId}, using default featured profile`);
      // Return default value if not found in database
      return 'web3tools';
    }

    // Parse featured profile - the script stores it directly as the value
    const featuredProfile = settings[0].value;
    if (featuredProfile && typeof featuredProfile === 'string') {
      return featuredProfile;
    }

    // Return default value if no featured profile found
    return 'web3tools';
  } catch (error) {
    console.error('Error getting featured profile:', error);
    // Return default value on error
    return 'web3tools';
  }
}
